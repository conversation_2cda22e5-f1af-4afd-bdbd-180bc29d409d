/* 页面整体布局 */
.page {
  width: 100%;
  min-height: 100vh;
  background-color: #ffffff;
}

.main-content {
  width: 100%;
  max-width: 1920px;
  margin: 0 auto;
}

/* 顶部导航栏 */
.header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 92px;
  padding: 0 240px;
  background-color: #ffffff;
}

.header-status {
  color: #ff1d1d;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  line-height: 20px;
}

.header-brand {
  display: flex;
  align-items: center;
  gap: 12px;
}

.brand-logo {
  width: 42px;
  height: 42px;
  border-radius: 50%;
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/a60a3781cd914f8fa46163367bc3f3b5_mergeImage.png);
  background-size: cover;
  border: 1px solid #979797;
}

.brand-name {
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  color: #000000;
  line-height: 42px;
  margin: 0;
}

.header-nav {
  display: flex;
  align-items: center;
  gap: 40px;
}

.nav-link {
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #000000;
  text-decoration: none;
  line-height: 22px;
  transition: color 0.2s;
}

.nav-link:hover {
  color: #0055c3;
}

.nav-icon {
  width: 14px;
  height: 14px;
}

/* 产品分类导航 */
.product-category-nav {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 212px;
  height: 92px;
  margin-left: 258px;
}

.category-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 118px;
  height: 92px;
  background-color: #0055c3;
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
}

.category-text {
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #ffffff;
  line-height: 25px;
}

.category-divider {
  width: 94px;
  height: 1px;
}

/* 主要横幅区域 */
.hero-banner {
  position: relative;
  width: 1440px;
  height: 382px;
  margin: 0 auto;
  margin-left: 240px;
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9dc429001f424f8c8297ac4025bd7600_mergeImage.png);
  background-size: cover;
  background-position: center;
  border: 1px solid #979797;
}

.hero-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding-top: 92px;
}

.hero-title {
  font-size: 48px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  color: #ffffff;
  line-height: 65px;
  text-align: center;
  margin: 0 0 55px 0;
}

.cta-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100px;
  height: 46px;
  background-color: #ec2914;
  border: none;
  cursor: pointer;
  transition: background-color 0.2s;
}

.cta-button:hover {
  background-color: #d12410;
}

.cta-text {
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #ffffff;
  line-height: 22px;
}

.hero-decoration {
  width: 128px;
  height: 14px;
  margin-top: 36px;
}

/* 产品对比卡片 */
.product-comparison-card {
  position: absolute;
  left: 181px;
  top: 0;
  width: 1090px;
  height: 246px;
  background-color: #ffffff;
  box-shadow: 0px -2px 15px 0px rgba(0, 0, 0, 0.2);
  padding: 24px 39px;
}

.product-icons {
  display: flex;
  justify-content: space-between;
  width: 222px;
  margin-bottom: 8px;
}

.product-icon {
  width: 30px;
  height: 30px;
}

.product-icon:last-child {
  height: 24px;
  margin-top: 3px;
}

.product-names {
  display: flex;
  justify-content: space-between;
  width: 387px;
  margin-bottom: 21px;
}

.product-name {
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  color: #000000;
  line-height: 25px;
}

.product-specs {
  display: flex;
  justify-content: space-between;
  width: 228px;
  margin-bottom: 8px;
}

.product-specs:last-child {
  margin-bottom: 0;
}

.spec-item {
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #000000;
  line-height: 20px;
}

/* 产品预览区域 */
.product-preview {
  width: 100%;
  height: 680px;
  margin-top: 60px;
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/a042b1e512f541e5b21632ac881ba922_mergeImage.png);
  background-size: cover;
  background-position: center;
  padding: 57px 0;
}

.preview-header {
  text-align: center;
  margin-bottom: 32px;
}

.preview-title {
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #000000;
  line-height: 42px;
  margin: 0;
}

.product-nav {
  display: flex;
  justify-content: center;
  gap: 80px;
  margin-bottom: 342px;
}

.product-nav-link {
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #000000;
  text-decoration: none;
  line-height: 20px;
  transition: color 0.2s;
}

.product-nav-link:hover {
  color: #0055c3;
}

.preview-status {
  text-align: center;
  color: #ff1d1d;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  line-height: 20px;
  margin-bottom: 9px;
}

.product-config {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16px;
  margin: 0 auto;
  max-width: 1064px;
}

.config-tabs {
  display: flex;
  flex-direction: column;
  gap: 30px;
}

.config-tab {
  width: 78px;
  height: 34px;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  line-height: 20px;
  border: 1px solid #000000;
  background-color: transparent;
  cursor: pointer;
  transition: all 0.2s;
}

.config-tab.active {
  background-color: #000000;
  color: #ffffff;
}

.config-tab:hover {
  background-color: #333333;
  color: #ffffff;
}

.product-image {
  width: 71px;
  height: 53px;
  margin: 0 16px;
}

.product-specs-grid {
  display: flex;
  align-items: center;
  gap: 30px;
}

.spec-group {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 7px;
}

.spec-value {
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  color: #000000;
  line-height: 21px;
}

.spec-value-complex {
  display: flex;
  align-items: center;
  font-size: 18px;
  line-height: 25px;
}

.spec-symbol {
  font-family: PingFangSC-Regular;
  color: #000000;
}

.spec-number {
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  color: #000000;
}

.spec-label {
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  color: #000000;
  line-height: 17px;
}

.spec-divider {
  width: 1px;
  height: 47px;
  background-color: #000000;
}

/* 公司介绍区域 */
.company-intro {
  text-align: center;
  padding: 57px 0;
  margin: 0 auto;
  max-width: 871px;
}

.company-title {
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #000000;
  line-height: 50px;
  margin: 0 0 15px 0;
}

.company-description {
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #000000;
  line-height: 27px;
  text-align: right;
  margin: 0;
}

/* 解决方案区域 */
.solutions-section {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 1030px;
  height: 120px;
  margin: 56px auto 0;
  background-color: #000000;
  padding: 0 206px;
}

.solutions-title {
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: #ffffff;
  line-height: 33px;
  margin: 0;
}

.solutions-cta {
  width: 511px;
  height: 120px;
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/4fedc05f24ff4840b1c29515f9b252bc_mergeImage.png);
  background-size: cover;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
}

.solutions-cta-text {
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  color: #ffffff;
  line-height: 33px;
}

/* 新闻区域 */
.news-section {
  padding: 57px 0 23px;
  margin: 0 auto;
  max-width: 1140px;
}

.news-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 23px;
}

.news-title {
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #000000;
  line-height: 50px;
  margin: 0;
}

.news-nav-links {
  display: flex;
  align-items: center;
  gap: 20px;
}

.news-nav-link {
  display: flex;
  align-items: center;
  gap: 10px;
  text-decoration: none;
  transition: opacity 0.2s;
}

.news-nav-link:hover {
  opacity: 0.8;
}

.news-nav-text {
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #767676;
  line-height: 20px;
}

.news-nav-link.active .news-nav-text {
  color: #003a85;
}

.news-nav-icon {
  width: 14px;
  height: 14px;
}

.news-divider {
  width: 77px;
  height: 1px;
}

/* 新闻卡片列表 */
.news-cards {
  display: flex;
  gap: 10px;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 1030px;
}

.news-card {
  width: 250px;
  height: 338px;
  background-color: #f4f4f4;
  display: flex;
  flex-direction: column;
}

.news-card-image {
  width: 100%;
  height: 128px;
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
  background-size: cover;
  background-position: center;
}

.news-card:nth-child(2) .news-card-image {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/7f3484660cef42f5974247db491f9c15_mergeImage.png);
}

.news-card:nth-child(3) .news-card-image {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/690b9c6f9fed426d944eabc8f93d97cd_mergeImage.png);
}

.news-card:nth-child(4) .news-card-image {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/5f98c304542a4f47ae19219690b3bed9_mergeImage.png);
}

.news-card-content {
  padding: 10px 20px 95px;
  flex: 1;
}

.news-card-title {
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #000000;
  line-height: 27px;
  margin: 0 0 7px 0;
}

.news-card-excerpt {
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  color: #000000;
  line-height: 22px;
  margin: 0;
}

/* 联系我们区域 */
.contact-section {
  width: 1030px;
  height: 170px;
  margin: 60px auto 0;
  background: url(https://lanhu-oss-proxy.lanhuapp.com/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158) 100% no-repeat;
  background-size: 100% 100%;
  padding: 26px 50px 60px;
}

.contact-header {
  margin-bottom: 3px;
}

.contact-title {
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #000000;
  line-height: 33px;
  margin: 0;
}

.contact-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
}

.contact-description {
  width: 639px;
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #000000;
  line-height: 20px;
  margin: 8px 0 0 0;
}

.contact-cta {
  width: 100px;
  height: 46px;
  background-color: #ec2914;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s;
}

.contact-cta:hover {
  background-color: #d12410;
}

.contact-cta-text {
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #ffffff;
  line-height: 22px;
}

/* 页脚区域 */
.footer {
  width: 1030px;
  height: 230px;
  margin: 86px auto 0;
  background: url(https://lanhu-oss-proxy.lanhuapp.com/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054) 100% no-repeat;
  background-size: 100% 100%;
  padding: 18px 60px 0;
  position: relative;
}

.footer-links {
  display: flex;
  gap: 110px;
  margin-bottom: 12px;
}

.footer-column {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.footer-column-title {
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  color: #ffffff;
  line-height: 22px;
  margin: 0;
}

.footer-link {
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #ffffff;
  text-decoration: none;
  line-height: 22px;
  transition: opacity 0.2s;
}

.footer-link:hover {
  opacity: 0.8;
}

.footer-brand {
  position: absolute;
  left: 20px;
  bottom: 88px;
  display: flex;
  align-items: center;
  gap: 4px;
}

.footer-logo {
  width: 56px;
  height: 55px;
}

.footer-brand-name {
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  color: #000000;
  line-height: 42px;
}

.footer-bottom {
  position: absolute;
  left: 246px;
  top: 170px;
  width: 1241px;
  height: 60px;
  background-color: #000000;
}

.bottom-nav {
  position: absolute;
  left: 214px;
  top: -78px;
  width: 1460px;
  height: 92px;
  background-color: #090909;
  display: flex;
  align-items: center;
  gap: 32px;
  padding-left: 32px;
}

.bottom-nav-item {
  display: flex;
  align-items: center;
  justify-content: center;
  height: 92px;
  padding: 0 32px;
}

.bottom-nav-item:first-child {
  background-color: #ffffff;
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  width: 135px;
}

.bottom-nav-item:first-child .bottom-nav-text {
  color: #000000;
}

.bottom-nav-item:last-child {
  background-color: #ffffff;
  width: 122px;
  margin-left: 570px;
}

.bottom-nav-item:last-child .bottom-nav-text {
  color: #000000;
}

.bottom-nav-text {
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  color: #ffffff;
  line-height: 25px;
}

/* 响应式设计 */
@media (max-width: 1920px) {
  .main-content {
    max-width: 100%;
  }

  .header {
    padding: 0 5%;
  }

  .hero-banner {
    margin-left: 5%;
    width: 90%;
  }

  .product-comparison-card {
    left: 5%;
    width: 80%;
  }
}





.text-wrapper_13 {
  background-color: rgba(0, 0, 0, 0);
  height: 34px;
  border: 1px solid rgba(0, 0, 0, 1);
  width: 78px;
  margin: 64px 0 0 40px;
}

.text_30 {
  width: 42px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 18px;
}

.text-group_22 {
  width: 64px;
  height: 48px;
  margin: 59px 0 0 252px;
}

.text_31 {
  width: 64px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 21px;
}

.text_32 {
  width: 24px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 9px;
}

.block_5 {
  width: 1px;
  height: 47px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 60px 0 0 30px;
}

.text-group_23 {
  width: 59px;
  height: 48px;
  margin: 59px 0 0 30px;
}

.text_33 {
  width: 42px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 21px;
}

.text_34 {
  width: 59px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 10px;
}

.block_6 {
  width: 1px;
  height: 47px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 60px 0 0 31px;
}

.text-group_24 {
  width: 62px;
  height: 49px;
  margin: 58px 0 0 30px;
}

.text-wrapper_14 {
  width: 62px;
  height: 25px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_35 {
  width: 62px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_36 {
  width: 62px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_37 {
  width: 24px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 7px;
}

.block_7 {
  width: 1px;
  height: 47px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 60px 0 0 30px;
}

.text-group_25 {
  width: 83px;
  height: 49px;
  margin: 58px 0 0 30px;
}

.text-wrapper_15 {
  width: 83px;
  height: 25px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_38 {
  width: 83px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_39 {
  width: 83px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_40 {
  width: 24px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 7px;
}

.text-group_26 {
  width: 871px;
  height: 119px;
  margin: 57px 0 0 513px;
}

.text_41 {
  width: 355px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
  margin-left: 270px;
}

.text_42 {
  width: 871px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: right;
  line-height: 27px;
  margin-top: 15px;
}

.block_8 {
  background-color: rgba(0, 0, 0, 1);
  width: 1030px;
  height: 120px;
  margin: 56px 0 0 433px;
}

.text_43 {
  width: 96px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 43px 0 0 206px;
}

.text-wrapper_16 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/4fedc05f24ff4840b1c29515f9b252bc_mergeImage.png);
  height: 120px;
  margin-left: 217px;
  width: 511px;
}

.text_44 {
  width: 96px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 43px 0 0 208px;
}

.group_13 {
  width: 1140px;
  height: 50px;
  margin: 57px 0 0 510px;
}

.text_45 {
  width: 142px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
}

.image-text_11 {
  width: 80px;
  height: 20px;
  margin: 22px 0 0 725px;
}

.text-group_6 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(118, 118, 118, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_2 {
  width: 14px;
  height: 14px;
  margin-top: 4px;
}

.image_4 {
  width: 77px;
  height: 1px;
  margin: 32px 0 0 20px;
}

.image-text_12 {
  width: 80px;
  height: 20px;
  margin: 22px 0 0 16px;
}

.text-group_7 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 58, 133, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_3 {
  width: 14px;
  height: 14px;
  margin-top: 4px;
}

.list_4 {
  width: 1030px;
  height: 338px;
  justify-content: space-between;
  margin: 23px 0 0 433px;
}

.image-text_3-0 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_1-0 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
}

.text-group_27-0 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_46-0 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 27px;
}

.text_47-0 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_3-1 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_1-1 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/7f3484660cef42f5974247db491f9c15_mergeImage.png);
}

.text-group_27-1 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_46-1 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 27px;
}

.text_47-1 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_3-2 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_1-2 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/690b9c6f9fed426d944eabc8f93d97cd_mergeImage.png);
}

.text-group_27-2 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_46-2 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 27px;
}

.text_47-2 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_3-3 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_1-3 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/5f98c304542a4f47ae19219690b3bed9_mergeImage.png);
}

.text-group_27-3 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_46-3 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 27px;
}

.text_47-3 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.group_6 {
  height: 170px;
  background: url(https://lanhu-oss-proxy.lanhuapp.com/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  margin: 60px 0 0 433px;
}

.text-wrapper_59 {
  width: 95px;
  height: 33px;
  margin: 26px 0 0 50px;
}

.text_56 {
  width: 95px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.section_3 {
  width: 859px;
  height: 48px;
  margin: 3px 0 60px 50px;
}

.text_57 {
  width: 639px;
  height: 40px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 20px;
  margin-top: 8px;
}

.text-wrapper_22 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
}

.text_58 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.block_11 {
  height: 230px;
  background: url(https://lanhu-oss-proxy.lanhuapp.com/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  position: relative;
  margin: 86px 0 0 433px;
}

.text-wrapper_60 {
  width: 409px;
  height: 22px;
  margin: 18px 0 0 60px;
}

.text_48 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_49 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_50 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text-wrapper_61 {
  width: 443px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_51 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_52 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_53 {
  width: 95px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 112px;
}

.text-wrapper_62 {
  width: 63px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_54 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text-wrapper_63 {
  width: 32px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_55 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.group_14 {
  width: 178px;
  height: 55px;
  margin: 30px 0 3px 20px;
}

.image-text_13 {
  width: 178px;
  height: 55px;
}

.image_5 {
  width: 56px;
  height: 55px;
}

.text-group_9 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin-top: 3px;
}

.box_6 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 246px;
  top: 170px;
  width: 1241px;
  height: 60px;
}

.block_13 {
  background-color: rgba(9, 9, 9, 1);
  position: absolute;
  left: 460px;
  top: 92px;
  width: 1460px;
  height: 92px;
}

.text-wrapper_23 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(255, 255, 255, 1);
  height: 92px;
  width: 135px;
}

.text_59 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.text_60 {
  width: 54px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.text_61 {
  width: 36px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 64px;
}

.text-wrapper_24 {
  background-color: rgba(255, 255, 255, 1);
  height: 92px;
  width: 122px;
  margin: 0 447px 0 570px;
}

.text_62 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 31px 0 0 26px;
}
