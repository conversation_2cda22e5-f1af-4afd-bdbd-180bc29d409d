.page {
  position: relative;
  width: 1920px;
  height: 2672px;
  overflow: hidden;
}

.box_1 {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 2672px;
}

.group_10 {
  width: 1633px;
  height: 92px;
}

.text_1 {
  width: 69px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-top: 54px;
}

.box_2 {
  border-radius: 50%;
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/a60a3781cd914f8fa46163367bc3f3b5_mergeImage.png);
  width: 42px;
  height: 42px;
  border: 1px solid rgba(151, 151, 151, 1);
  margin: 23px 0 0 111px;
}

.text_2 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin: 21px 0 0 12px;
}

.box_3 {
  background-color: rgba(255, 255, 255, 1);
  width: 1240px;
  height: 92px;
  margin-left: 41px;
}

.text_3 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 266px;
}

.text_4 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 39px;
}

.text_5 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.text_6 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.text_7 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 34px 0 0 40px;
}

.thumbnail_1 {
  width: 14px;
  height: 14px;
  margin: 39px 446px 0 40px;
}

.group_11 {
  width: 212px;
  height: 92px;
  margin-left: 258px;
}

.text-wrapper_1 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(0, 85, 195, 1);
  height: 92px;
  width: 118px;
}

.text_8 {
  width: 54px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.image_1 {
  width: 94px;
  height: 1px;
  margin-top: 45px;
}

.block_3 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9dc429001f424f8c8297ac4025bd7600_mergeImage.png);
  position: relative;
  width: 1440px;
  height: 382px;
  border: 1px solid rgba(151, 151, 151, 1);
  margin-left: 240px;
}

.text_9 {
  width: 838px;
  height: 65px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 48px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 65px;
  margin: 92px 0 0 292px;
}

.text-wrapper_2 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
  margin: 55px 0 0 670px;
}

.text_10 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.image_2 {
  width: 128px;
  height: 14px;
  margin: 91px 0 19px 656px;
}

.section_1 {
  box-shadow: 0px -2px 15px 0px rgba(0, 0, 0, 0.2);
  background-color: rgba(255, 255, 255, 1);
  height: 246px;
  width: 1090px;
  position: absolute;
  left: 181px;
  top: 0;
}

.image-wrapper_4 {
  width: 222px;
  height: 30px;
  margin: 24px 0 0 39px;
}

.label_1 {
  width: 30px;
  height: 30px;
}

.label_2 {
  width: 30px;
  height: 24px;
  margin-top: 3px;
}

.text-wrapper_51 {
  width: 387px;
  height: 25px;
  margin: 8px 0 0 39px;
}

.text_11 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_12 {
  width: 195px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text-wrapper_52 {
  width: 228px;
  height: 20px;
  margin: 21px 0 0 39px;
}

.text_13 {
  width: 36px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_14 {
  width: 36px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text-wrapper_53 {
  width: 228px;
  height: 20px;
  margin: 8px 0 0 39px;
}

.text_15 {
  width: 36px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_16 {
  width: 36px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text-wrapper_54 {
  width: 228px;
  height: 20px;
  margin: 8px 0 0 39px;
}

.text_17 {
  width: 36px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_18 {
  width: 36px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text-wrapper_55 {
  width: 228px;
  height: 20px;
  margin: 8px 0 34px 39px;
}

.text_19 {
  width: 36px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_20 {
  width: 36px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.block_4 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/a042b1e512f541e5b21632ac881ba922_mergeImage.png);
  height: 680px;
  margin-top: 60px;
  width: 1920px;
}

.text-wrapper_56 {
  width: 119px;
  height: 42px;
  margin: 57px 0 0 901px;
}

.text_21 {
  width: 119px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
}

.text-wrapper_57 {
  width: 682px;
  height: 20px;
  margin: 32px 0 0 619px;
}

.text_22 {
  width: 111px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.text_23 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 80px;
}

.text_24 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 80px;
}

.text_25 {
  width: 83px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 80px;
}

.text_26 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin-left: 80px;
}

.text-wrapper_58 {
  width: 69px;
  height: 20px;
  margin: 342px 0 0 439px;
}

.text_27 {
  width: 69px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 29, 29, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.group_12 {
  width: 1064px;
  height: 107px;
  margin: 9px 0 51px 436px;
}

.text-wrapper_11 {
  background-color: rgba(0, 0, 0, 1);
  height: 34px;
  width: 78px;
}

.text_28 {
  width: 42px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 18px;
}

.image_3 {
  width: 71px;
  height: 53px;
  margin: 25px 0 0 16px;
}

.text-wrapper_12 {
  background-color: rgba(0, 0, 0, 0);
  height: 34px;
  border: 1px solid rgba(0, 0, 0, 1);
  width: 78px;
  margin: 64px 0 0 -1px;
}

.text_29 {
  width: 42px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 18px;
}

.text-wrapper_13 {
  background-color: rgba(0, 0, 0, 0);
  height: 34px;
  border: 1px solid rgba(0, 0, 0, 1);
  width: 78px;
  margin: 64px 0 0 40px;
}

.text_30 {
  width: 42px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
  margin: 6px 0 0 18px;
}

.text-group_22 {
  width: 64px;
  height: 48px;
  margin: 59px 0 0 252px;
}

.text_31 {
  width: 64px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 21px;
}

.text_32 {
  width: 24px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 9px;
}

.block_5 {
  width: 1px;
  height: 47px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 60px 0 0 30px;
}

.text-group_23 {
  width: 59px;
  height: 48px;
  margin: 59px 0 0 30px;
}

.text_33 {
  width: 42px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 21px;
}

.text_34 {
  width: 59px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 10px;
}

.block_6 {
  width: 1px;
  height: 47px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 60px 0 0 31px;
}

.text-group_24 {
  width: 62px;
  height: 49px;
  margin: 58px 0 0 30px;
}

.text-wrapper_14 {
  width: 62px;
  height: 25px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_35 {
  width: 62px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_36 {
  width: 62px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_37 {
  width: 24px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 7px;
}

.block_7 {
  width: 1px;
  height: 47px;
  border: 1px solid rgba(0, 0, 0, 1);
  margin: 60px 0 0 30px;
}

.text-group_25 {
  width: 83px;
  height: 49px;
  margin: 58px 0 0 30px;
}

.text-wrapper_15 {
  width: 83px;
  height: 25px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_38 {
  width: 83px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: PingFangSC-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_39 {
  width: 83px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: Helvetica, "Microsoft YaHei", Arial, sans-serif;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
}

.text_40 {
  width: 24px;
  height: 17px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 12px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 17px;
  margin-top: 7px;
}

.text-group_26 {
  width: 871px;
  height: 119px;
  margin: 57px 0 0 513px;
}

.text_41 {
  width: 355px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
  margin-left: 270px;
}

.text_42 {
  width: 871px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: right;
  line-height: 27px;
  margin-top: 15px;
}

.block_8 {
  background-color: rgba(0, 0, 0, 1);
  width: 1030px;
  height: 120px;
  margin: 56px 0 0 433px;
}

.text_43 {
  width: 96px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 43px 0 0 206px;
}

.text-wrapper_16 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/4fedc05f24ff4840b1c29515f9b252bc_mergeImage.png);
  height: 120px;
  margin-left: 217px;
  width: 511px;
}

.text_44 {
  width: 96px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 24px;
  font-family: PingFangSC-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
  margin: 43px 0 0 208px;
}

.group_13 {
  width: 1140px;
  height: 50px;
  margin: 57px 0 0 510px;
}

.text_45 {
  width: 142px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 36px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 50px;
}

.image-text_11 {
  width: 80px;
  height: 20px;
  margin: 22px 0 0 725px;
}

.text-group_6 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(118, 118, 118, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_2 {
  width: 14px;
  height: 14px;
  margin-top: 4px;
}

.image_4 {
  width: 77px;
  height: 1px;
  margin: 32px 0 0 20px;
}

.image-text_12 {
  width: 80px;
  height: 20px;
  margin: 22px 0 0 16px;
}

.text-group_7 {
  width: 56px;
  height: 20px;
  overflow-wrap: break-word;
  color: rgba(0, 58, 133, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 20px;
}

.thumbnail_3 {
  width: 14px;
  height: 14px;
  margin-top: 4px;
}

.list_4 {
  width: 1030px;
  height: 338px;
  justify-content: space-between;
  margin: 23px 0 0 433px;
}

.image-text_3-0 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_1-0 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
}

.text-group_27-0 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_46-0 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 27px;
}

.text_47-0 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_3-1 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_1-1 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/7f3484660cef42f5974247db491f9c15_mergeImage.png);
}

.text-group_27-1 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_46-1 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 27px;
}

.text_47-1 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_3-2 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_1-2 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/690b9c6f9fed426d944eabc8f93d97cd_mergeImage.png);
}

.text-group_27-2 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_46-2 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 27px;
}

.text_47-2 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.image-text_3-3 {
  background-color: rgba(244, 244, 244, 1);
  width: 250px;
  height: 338px;
  margin-right: 10px;
}

.group_1-3 {
  background-image: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/9658974e4e9d4ac0855add528a2e10b2_mergeImage.png);
  width: 250px;
  height: 128px;
  background: url(https://lanhu-dds-backend.oss-cn-beijing.aliyuncs.com/merge_image/imgs/5f98c304542a4f47ae19219690b3bed9_mergeImage.png);
}

.text-group_27-3 {
  width: 208px;
  height: 105px;
  margin: 10px 0 95px 20px;
}

.text_46-3 {
  width: 208px;
  height: 54px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 20px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 27px;
}

.text_47-3 {
  width: 208px;
  height: 44px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Light;
  font-weight: 300;
  text-align: left;
  line-height: 22px;
  margin-top: 7px;
}

.group_6 {
  height: 170px;
  background: url(https://lanhu-oss-proxy.lanhuapp.com/SketchPng1c26c087dcb080c171d06745e5afc6bf9e0a1bbd0f8e73514986c9c3afca5158)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  margin: 60px 0 0 433px;
}

.text-wrapper_59 {
  width: 95px;
  height: 33px;
  margin: 26px 0 0 50px;
}

.text_56 {
  width: 95px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 24px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 33px;
}

.section_3 {
  width: 859px;
  height: 48px;
  margin: 3px 0 60px 50px;
}

.text_57 {
  width: 639px;
  height: 40px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 14px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  line-height: 20px;
  margin-top: 8px;
}

.text-wrapper_22 {
  background-color: rgba(236, 41, 20, 1);
  height: 46px;
  width: 100px;
}

.text_58 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin: 10px 0 0 19px;
}

.block_11 {
  height: 230px;
  background: url(https://lanhu-oss-proxy.lanhuapp.com/SketchPngc665d90b5754568cc23004f00a8304b254b404ddd99665b5d13240330c29f054)
    100% no-repeat;
  background-size: 100% 100%;
  width: 1030px;
  position: relative;
  margin: 86px 0 0 433px;
}

.text-wrapper_60 {
  width: 409px;
  height: 22px;
  margin: 18px 0 0 60px;
}

.text_48 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_49 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_50 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Bold;
  font-weight: 700;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text-wrapper_61 {
  width: 443px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_51 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text_52 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 110px;
}

.text_53 {
  width: 95px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
  margin-left: 112px;
}

.text-wrapper_62 {
  width: 63px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_54 {
  width: 63px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.text-wrapper_63 {
  width: 32px;
  height: 22px;
  margin: 12px 0 0 70px;
}

.text_55 {
  width: 32px;
  height: 22px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 16px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 22px;
}

.group_14 {
  width: 178px;
  height: 55px;
  margin: 30px 0 3px 20px;
}

.image-text_13 {
  width: 178px;
  height: 55px;
}

.image_5 {
  width: 56px;
  height: 55px;
}

.text-group_9 {
  width: 118px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 30px;
  font-family: AlibabaPuHuiTi-Medium;
  font-weight: 500;
  text-align: left;
  white-space: nowrap;
  line-height: 42px;
  margin-top: 3px;
}

.box_6 {
  background-color: rgba(0, 0, 0, 1);
  position: absolute;
  left: 246px;
  top: 170px;
  width: 1241px;
  height: 60px;
}

.block_13 {
  background-color: rgba(9, 9, 9, 1);
  position: absolute;
  left: 460px;
  top: 92px;
  width: 1460px;
  height: 92px;
}

.text-wrapper_23 {
  box-shadow: -3px -10px 18px 0px rgba(0, 0, 0, 0.14);
  background-color: rgba(255, 255, 255, 1);
  height: 92px;
  width: 135px;
}

.text_59 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.text_60 {
  width: 54px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 32px;
}

.text_61 {
  width: 36px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 32px 0 0 64px;
}

.text-wrapper_24 {
  background-color: rgba(255, 255, 255, 1);
  height: 92px;
  width: 122px;
  margin: 0 447px 0 570px;
}

.text_62 {
  width: 71px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 18px;
  font-family: AlibabaPuHuiTi-Regular;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 25px;
  margin: 31px 0 0 26px;
}
